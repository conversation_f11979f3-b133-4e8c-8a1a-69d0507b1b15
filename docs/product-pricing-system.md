# Product Pricing System Documentation

## Overview

The EverTrek application uses a multi-currency pricing system that supports automatic currency conversion, manual price overrides, and dynamic pricing display. The system is built around a base currency (typically USD) with automatic conversion to other supported currencies using exchange rates.

## Database Structure

### Core Tables

#### `#__zencurrencies`
Stores available currencies in the system.

**Key Fields:**
- `id` - Primary key
- `code` - 3-character ISO currency code (e.g., 'USD', 'GBP', 'EUR')
- `symbol` - Currency symbol (e.g., '$', '£', '€')
- `title` - Full currency name
- `state` - Published status (1 = active, 0 = inactive)

#### `#__zencurrencyrates`
Stores exchange rates between currencies with historical tracking.

**Key Fields:**
- `from` - Source currency code
- `to` - Target currency code
- `rate` - Exchange rate multiplier
- `valid_from` - Date when rate becomes effective
- `rounding` - Rounding rules for converted prices

#### `#__zenholidayprices`
Stores pricing for holiday packages in multiple currencies.

**Key Fields:**
- `date_id` - Links to specific departure date
- `type_id` - Links to price type (adult, child, etc.)
- `currency_code` - Currency for this price
- `value` - Price amount
- `previous_value` - Previous price (for showing discounts)
- `additional_value` - Deposit amount
- `state` - Active status

## Pricing Architecture

### Base Currency System

The system operates on a **base currency** model:

1. **Base Currency**: Set in component parameters (`com_zenadmin`), typically USD
2. **Manual Pricing**: Prices are manually entered in the base currency
3. **Automatic Conversion**: Other currency prices are automatically generated using exchange rates
4. **Rate Updates**: When base currency prices change, all derived prices are regenerated

### Forex Pricing Plugin

The `ForexPricing` plugin (`plugins/mrzen/forexpricing/`) handles automatic price conversion:

**Key Functions:**
- Monitors base currency price changes
- Automatically creates/updates prices in all active currencies
- Prevents manual editing of auto-generated prices
- Applies rounding rules per currency

### Price Generation Process

1. Admin enters price in base currency
2. Plugin detects the change via `onContentAfterSave` event
3. System retrieves latest exchange rates for all active currencies
4. Prices calculated: `new_price = base_price * exchange_rate`
5. Rounding applied based on currency settings
6. New prices inserted into `#__zenholidayprices` table

## Frontend Display

### Currency Detection

User's currency is determined by priority:

1. **URL Parameter**: `?currency=GBP`
2. **Session Storage**: Previously selected currency
3. **GeoIP Detection**: Based on user's location
4. **Default Fallback**: System default currency

Implementation in `ZenSessionHelper::getUsersCurrencyCode()`

### Price Formatting

**Angular Filters** (in `libraries/mrzen/assets/js/TravelZen.js`):

- `price` - Basic price formatting with currency symbol
- `as_price` - Advanced formatting with locale support
- `thousands` - Number formatting with thousands separators

**PHP Helpers**:
- `ZenMoneyHelper` - Server-side currency utilities
- Currency symbols and formatting rules

### Currency Switcher

The `mod_zencurrencyswitcher` module provides:
- Dropdown list of active currencies
- Flag icons for visual identification
- Form submission to change user's currency
- Session persistence of selection

## Adding New Currencies

### Step 1: Add Currency Record

1. Navigate to **Administrator → Components → Zen Admin → Currencies**
2. Click **New** to create currency
3. Fill required fields:
   - **Code**: 3-letter ISO code (e.g., 'CAD')
   - **Symbol**: Currency symbol (e.g., 'C$')
   - **Title**: Full name (e.g., 'Canadian Dollar')
   - **State**: Set to **Published**

### Step 2: Add Exchange Rates

1. Go to **Administrator → Components → Zen Admin → Currency Rates**
2. Create rate from base currency to new currency:
   - **From**: Base currency (e.g., 'USD')
   - **To**: New currency (e.g., 'CAD')
   - **Rate**: Exchange rate (e.g., 1.35 for USD to CAD)
   - **Valid From**: Effective date
   - **Rounding**: Rounding increment (e.g., 1.00 for whole numbers)

### Step 3: Generate Prices

**Option A - Automatic (Recommended):**
- Edit any base currency price
- Plugin automatically generates all currency prices

**Option B - Manual Bulk Generation:**
- Use admin interface: **Tools → Generate All Forex Pricing**
- Processes all future-dated prices

### Step 4: Verify Display

1. Check currency appears in frontend switcher
2. Verify prices display correctly
3. Test currency switching functionality
4. Confirm price formatting matches expectations

## Configuration Options

### Component Parameters

In **Administrator → Components → Zen Admin → Options**:

- `base_currency` - Base currency for manual price entry
- `geoip_default_currency` - Fallback when GeoIP fails

### Domain-Specific Settings

Per-domain currency settings in `#__zendomains`:
- Default currency per website
- Currency grouping preferences
- Locale-specific formatting rules

## Technical Implementation Notes

### Price Storage

- All prices stored as DECIMAL(10,2) for precision
- Converted prices marked as auto-generated
- Manual editing disabled for non-base currencies

### Performance Considerations

- Exchange rates cached with date-based validity
- Frontend uses Angular filters for client-side formatting
- Database queries optimized with proper indexing

### Error Handling

- Missing exchange rates prevent price generation
- Invalid currencies fall back to base currency
- Rounding errors minimized through proper decimal handling

## Troubleshooting

### Common Issues

1. **Prices not converting**: Check exchange rates exist and are current
2. **Wrong currency showing**: Verify GeoIP configuration and session handling
3. **Formatting issues**: Check browser locale support and currency symbols
4. **Missing currencies**: Ensure currency is published and has valid rates

### Debugging Tools

- Enable plugin debugging in `forexpricing.php`
- Check browser console for Angular filter errors
- Monitor database for price generation logs
- Verify session currency storage

## Geotargeted Currency Display

### Current Challenges with Caching

The application uses multiple layers of caching that present challenges for geotargeting:

1. **ZenCache Plugin**: Locale-aware caching that shards by currency and language
2. **JCH Optimize**: Page-level caching with CDN integration
3. **CDN Caching**: Static asset delivery with geographic distribution

**Current Cache Key Structure** (from `ZenCache`):
```
page:domain.com:path:currency.GBP:language.en:mobile.false:tablet.false
```

### Geotargeting Implementation Strategies

#### Strategy 1: JavaScript-Based Currency Detection (Recommended)

**Approach**: Use client-side geolocation with cache-friendly implementation.

**Implementation Steps**:

1. **Server-Side Preparation**:
   - Serve pages with default currency (USD/GBP)
   - Include all currency data in JavaScript configuration
   - Set cache headers for geographic regions

2. **Client-Side Detection**:
   ```javascript
   // Add to TravelZen.js
   TravelZen.GeoTargeting = {
     detectCurrency: function() {
       // Check for existing cookie first
       var savedCurrency = this.getCurrencyFromCookie();
       if (savedCurrency) return savedCurrency;

       // Use browser geolocation API
       if (navigator.geolocation) {
         navigator.geolocation.getCurrentPosition(
           this.handleGeolocationSuccess.bind(this),
           this.handleGeolocationError.bind(this)
         );
       } else {
         // Fallback to IP-based detection
         this.detectByIP();
       }
     },

     detectByIP: function() {
       // Use third-party service or CloudFlare headers
       fetch('/api/detect-currency')
         .then(response => response.json())
         .then(data => this.applyCurrency(data.currency));
     }
   };
   ```

3. **Dynamic Price Updates**:
   ```javascript
   // Update prices without page reload
   TravelZen.updatePricesForCurrency = function(newCurrency) {
     // Update TravelZen.currency object
     TravelZen.currency = currencyData[newCurrency];

     // Trigger Angular digest to update all price filters
     angular.element(document).scope().$apply();

     // Update search results if on search page
     if (typeof ElasticSearchService !== 'undefined') {
       ElasticSearchService.updateCurrency(newCurrency);
     }
   };
   ```

#### Strategy 2: Edge Computing with CloudFlare Workers

**Approach**: Use CloudFlare Workers to modify responses based on visitor location.

**Implementation**:

1. **CloudFlare Worker Script**:
   ```javascript
   addEventListener('fetch', event => {
     event.respondWith(handleRequest(event.request))
   })

   async function handleRequest(request) {
     const country = request.cf.country
     const currency = getCurrencyForCountry(country)

     // Modify HTML to include correct currency
     const response = await fetch(request)
     const html = await response.text()

     const modifiedHtml = html.replace(
       /TravelZen\.currency\s*=\s*{[^}]+}/,
       `TravelZen.currency = ${JSON.stringify(currencyData[currency])}`
     )

     return new Response(modifiedHtml, {
       headers: response.headers
     })
   }
   ```

2. **Cache Segmentation**:
   - Cache by country code: `cache-key-US`, `cache-key-GB`, etc.
   - Reduce cache variations to major currency regions
   - Use CloudFlare's geographic caching

#### Strategy 3: Hybrid Server-Client Approach

**Approach**: Combine server-side detection with client-side updates.

**Implementation**:

1. **Enhanced ZenCache Key**:
   ```php
   // Modify ZenCache::getKey() to include country
   private function getKey(){
       $uri = clone JUri::getInstance();

       // Get country from CloudFlare header or GeoIP
       $country = $this->getVisitorCountry();
       $currency = $this->getCurrencyForCountry($country);

       $uri->setVar('currency', $currency);
       $uri->setVar('country', $country);
       // ... rest of existing logic
   }

   private function getVisitorCountry() {
       // Priority order:
       // 1. CloudFlare CF-IPCountry header
       // 2. Existing GeoIP lookup
       // 3. Default country

       if (isset($_SERVER['HTTP_CF_IPCOUNTRY'])) {
           return $_SERVER['HTTP_CF_IPCOUNTRY'];
       }

       $geo = new ZenGeolocationHelper();
       $country = $geo->getCountry();
       return $country ? $country->code : 'US';
   }
   ```

2. **Currency Override Mechanism**:
   ```php
   // Allow manual currency selection to override geotargeting
   public static function getUsersCurrencyCode() {
       // 1. Manual selection (URL/session)
       $manual = self::getManualCurrencySelection();
       if ($manual) return $manual;

       // 2. Geotargeted currency
       $geo = self::getGeotargetedCurrency();
       if ($geo) return $geo;

       // 3. Default fallback
       return self::getDefaultCurrency();
   }
   ```

### Recommended Implementation Plan

#### Phase 1: JavaScript Enhancement (Low Risk)

1. **Enhance Currency Switcher**:
   - Add automatic detection on first visit
   - Store preference in long-term cookie
   - Respect manual selections

2. **Update Angular Filters**:
   - Make price filters reactive to currency changes
   - Add loading states during currency switches
   - Cache converted prices client-side

#### Phase 2: Cache Optimization (Medium Risk)

1. **Reduce Cache Variations**:
   - Group countries by currency regions
   - Use 4-5 major currencies instead of all currencies
   - Implement cache warming for popular regions

2. **Smart Cache Keys**:
   ```php
   // Group countries into currency regions
   $currencyRegions = [
       'USD' => ['US', 'CA', 'MX'],
       'EUR' => ['DE', 'FR', 'IT', 'ES'],
       'GBP' => ['GB', 'IE'],
       'AUD' => ['AU', 'NZ']
   ];
   ```

#### Phase 3: Edge Computing (Advanced)

1. **CloudFlare Workers**:
   - Deploy geotargeting logic to edge
   - Maintain cache efficiency
   - Handle currency switching gracefully

2. **Advanced Analytics**:
   - Track currency conversion rates by region
   - Monitor cache hit rates
   - A/B test geotargeting effectiveness

### Technical Considerations

#### Cache Invalidation Strategy

1. **Selective Invalidation**:
   ```php
   // When prices change, invalidate specific currency caches
   public function invalidateCurrencyCache($currencyCode) {
       $cache = new ZenCache();
       $pattern = "page:*:currency.{$currencyCode}:*";
       $cache->deletePattern($pattern);
   }
   ```

2. **Gradual Rollout**:
   - Test with small percentage of traffic
   - Monitor cache hit rates and performance
   - Gradually increase geotargeting coverage

#### Performance Monitoring

1. **Key Metrics**:
   - Cache hit rate by region
   - Currency detection accuracy
   - Page load times with geotargeting
   - Conversion rates by currency

2. **Fallback Mechanisms**:
   - Always provide default currency option
   - Handle geolocation failures gracefully
   - Maintain manual override capability

### Configuration Options

#### New Component Parameters

Add to `com_zenadmin` configuration:

```xml
<field name="geotargeting_enabled" type="radio"
       label="Enable Currency Geotargeting"
       default="0">
    <option value="0">Disabled</option>
    <option value="1">JavaScript Only</option>
    <option value="2">Server-Side</option>
    <option value="3">Hybrid</option>
</field>

<field name="geotargeting_cache_regions" type="textarea"
       label="Currency Regions"
       description="JSON mapping of countries to currencies"
       default='{"US":"USD","GB":"GBP","DE":"EUR"}' />
```

#### Domain-Specific Settings

```php
// Add to #__zendomains table
ALTER TABLE #__zendomains ADD COLUMN geotargeting_config TEXT;

// Store per-domain geotargeting rules
{
  "enabled": true,
  "default_currency": "USD",
  "country_overrides": {
    "GB": "GBP",
    "DE": "EUR"
  },
  "cache_strategy": "javascript"
}
```

## Cart/Checkout Integration

### External Checkout System Architecture

The EverTrek application uses a **decoupled architecture** where the main Joomla site handles product display and pricing, while an external checkout system (hosted separately) manages the booking and payment process.

#### Key Integration Components

**1. ZenProvider Component (`com_zenprovider`)**
- Acts as a **Product API** for external checkout systems
- Provides authenticated access to product and pricing data
- Handles booking reservations and availability management
- Uses HMAC-SHA256 signature authentication for security

**2. Data Feed System (`com_zenholidays` datafeeds)**
- Exports product and pricing data in various formats (JSON, XML)
- Supports multiple currencies and custom templates
- Access-controlled via unique API keys
- Used for syncing product catalogs with external systems

**3. Legacy Booking System (`ZenBooking` class)**
- References `com_zensales` component for order processing
- Handles payment data encryption and storage
- Manages booking confirmations and email notifications

### ZenProvider API Integration

#### Authentication Mechanism

External systems authenticate using **signed requests**:

```
X-Request-Signature: {public_key} {timestamp} {signature}
```

Where:
- `public_key`: Identifies the external system
- `timestamp`: Request timestamp (5-minute validity window)
- `signature`: HMAC-SHA256 of timestamp + request body using private key

#### API Endpoints

**Product Catalog** (`/index.php?option=com_zenprovider&view=product&format=json&id={product_id}`):
```json
{
  "$schema": "https://schema.travelzen.app/1.0/product-provider/package",
  "id": "holiday:123-1",
  "sku": "EBC001",
  "group_name": "Everest Base Camp Trek",
  "pricing": [
    {
      "price": {
        "currency": "GBP",
        "value": 299500
      },
      "deposit": {
        "currency": "GBP",
        "value": 20000
      }
    }
  ],
  "service_dates": {
    "start": "2024-03-15T00:00:00+00:00",
    "end": "2024-03-29T00:00:00+00:00",
    "kind": "fixed"
  }
}
```

**Booking Reservations** (`/index.php?option=com_zenprovider&view=book&format=json`):
- Creates temporary reservations
- Manages availability deduction
- Returns booking reference for external system

#### Multi-Currency Support

The ZenProvider API supports both single and multi-currency modes:

**Single Currency Mode**: Returns prices in one currency per request
**Multi-Currency Mode**: Returns all available currency prices in single response

```php
// Multi-currency pricing structure
"pricing": [
  {
    "price": {"currency": "GBP", "value": 299500},
    "deposit": {"currency": "GBP", "value": 20000}
  },
  {
    "price": {"currency": "USD", "value": 374375},
    "deposit": {"currency": "USD", "value": 25000}
  }
]
```

### Data Feed Integration

#### Feed Configuration

Data feeds are configured in the admin interface with:
- **Access Key**: Unique identifier for external system access
- **Currency**: Primary currency for the feed
- **Template**: Output format (JSON, XML, custom)
- **Multi-currency**: Whether to include all currency prices

#### Feed URLs

**Holiday List**: `/index.php?option=com_zenholidays&view=datafeeds&format=json&key={access_key}`
**Individual Holiday**: `/index.php?option=com_zenholidays&view=datafeed&format=json&id={holiday_id}&key={access_key}`

#### Currency Handling in Feeds

Feeds respect the configured currency and can operate in two modes:

1. **Single Currency**: Prices returned in feed's configured currency only
2. **Multi-Currency**: All available currency prices included in response

### Pricing Data Synchronization

#### Real-time vs Batch Sync

**Real-time Integration** (ZenProvider API):
- Live pricing data with current exchange rates
- Immediate availability updates
- Suitable for checkout systems requiring current pricing

**Batch Integration** (Data Feeds):
- Periodic catalog synchronization
- Suitable for external inventory systems
- May have slight pricing delays due to caching

#### Currency Consistency

**Challenge**: Ensuring external checkout shows same prices as main site

**Solutions**:
1. **API-First Approach**: External system calls ZenProvider API for real-time pricing
2. **Frequent Sync**: Regular data feed updates (hourly/daily)
3. **Cache Invalidation**: Coordinate cache clearing between systems
4. **Currency Locking**: Lock currency at start of booking process

### External System Requirements

#### For secure.evertrek.co.uk Integration

Based on the codebase analysis, external checkout systems should:

**1. Authentication Setup**:
- Obtain public/private key pair from ZenProvider credentials
- Implement HMAC-SHA256 request signing
- Handle authentication failures gracefully

**2. Product Data Retrieval**:
- Use ZenProvider API for real-time product/pricing data
- Support multi-currency pricing display
- Handle availability checking before booking

**3. Booking Process**:
- Create reservations via ZenProvider book endpoint
- Store booking references for order tracking
- Handle availability conflicts appropriately

**4. Currency Handling**:
- Respect user's selected currency from main site
- Display prices in consistent currency throughout checkout
- Handle currency conversion transparently

### Implementation Considerations

#### Security

**API Security**:
- All requests must be signed with valid credentials
- Timestamp validation prevents replay attacks
- Private keys encrypted in database

**Data Protection**:
- Payment card data masked in booking system
- Sensitive data encrypted with component-specific keys
- Audit trail for all booking transactions

#### Performance

**Caching Strategy**:
- Product data cached with currency-specific keys
- API responses cached at external system level
- Coordinate cache invalidation between systems

**Availability Management**:
- Real-time availability checking via API
- Optimistic locking for booking conflicts
- Graceful handling of sold-out scenarios

#### Error Handling

**Common Integration Issues**:
1. **Authentication Failures**: Invalid signatures or expired timestamps
2. **Currency Mismatches**: Requested currency not available
3. **Availability Conflicts**: Product sold out between display and booking
4. **Pricing Inconsistencies**: Exchange rate updates during booking process

**Recommended Solutions**:
- Implement retry logic with exponential backoff
- Validate currency availability before displaying prices
- Lock pricing at start of checkout process
- Provide clear error messages to users

### Monitoring and Maintenance

#### Key Metrics

**API Performance**:
- Response times for product and booking endpoints
- Authentication success/failure rates
- Currency conversion accuracy

**Data Consistency**:
- Price variance between main site and external checkout
- Booking success rates
- Currency display consistency

#### Troubleshooting Tools

**Admin Interface**:
- ZenProvider credentials management
- Data feed access logs
- Booking reservation tracking

**Debugging**:
- API request/response logging
- Currency conversion audit trails
- Availability tracking logs

## Future Enhancements

### Potential Improvements

1. **API Integration**: Automatic exchange rate updates from financial APIs
2. **Historical Rates**: Track rate changes over time
3. **Dynamic Pricing**: Time-based or demand-based pricing adjustments
4. **Multi-Base Support**: Support multiple base currencies simultaneously
5. **Advanced Rounding**: More sophisticated rounding rules per market
6. **Machine Learning**: Predictive currency preferences based on user behavior
7. **Real-time Rates**: Live exchange rate updates for volatile currencies
8. **Webhook Integration**: Real-time notifications for booking status changes
9. **GraphQL API**: More flexible data querying for external systems
10. **Microservices Architecture**: Separate pricing service for better scalability
