# Product Pricing System Documentation

## Overview

The EverTrek application uses a multi-currency pricing system that supports automatic currency conversion, manual price overrides, and dynamic pricing display. The system is built around a base currency (typically USD) with automatic conversion to other supported currencies using exchange rates.

## Database Structure

### Core Tables

#### `#__zencurrencies`
Stores available currencies in the system.

**Key Fields:**
- `id` - Primary key
- `code` - 3-character ISO currency code (e.g., 'USD', 'GBP', 'EUR')
- `symbol` - Currency symbol (e.g., '$', '£', '€')
- `title` - Full currency name
- `state` - Published status (1 = active, 0 = inactive)

#### `#__zencurrencyrates`
Stores exchange rates between currencies with historical tracking.

**Key Fields:**
- `from` - Source currency code
- `to` - Target currency code
- `rate` - Exchange rate multiplier
- `valid_from` - Date when rate becomes effective
- `rounding` - Rounding rules for converted prices

#### `#__zenholidayprices`
Stores pricing for holiday packages in multiple currencies.

**Key Fields:**
- `date_id` - Links to specific departure date
- `type_id` - Links to price type (adult, child, etc.)
- `currency_code` - Currency for this price
- `value` - Price amount
- `previous_value` - Previous price (for showing discounts)
- `additional_value` - Deposit amount
- `state` - Active status

## Pricing Architecture

### Base Currency System

The system operates on a **base currency** model:

1. **Base Currency**: Set in component parameters (`com_zenadmin`), typically USD
2. **Manual Pricing**: Prices are manually entered in the base currency
3. **Automatic Conversion**: Other currency prices are automatically generated using exchange rates
4. **Rate Updates**: When base currency prices change, all derived prices are regenerated

### Forex Pricing Plugin

The `ForexPricing` plugin (`plugins/mrzen/forexpricing/`) handles automatic price conversion:

**Key Functions:**
- Monitors base currency price changes
- Automatically creates/updates prices in all active currencies
- Prevents manual editing of auto-generated prices
- Applies rounding rules per currency

### Price Generation Process

1. Admin enters price in base currency
2. Plugin detects the change via `onContentAfterSave` event
3. System retrieves latest exchange rates for all active currencies
4. Prices calculated: `new_price = base_price * exchange_rate`
5. Rounding applied based on currency settings
6. New prices inserted into `#__zenholidayprices` table

## Frontend Display

### Currency Detection

User's currency is determined by priority:

1. **URL Parameter**: `?currency=GBP`
2. **Session Storage**: Previously selected currency
3. **GeoIP Detection**: Based on user's location
4. **Default Fallback**: System default currency

Implementation in `ZenSessionHelper::getUsersCurrencyCode()`

### Price Formatting

**Angular Filters** (in `libraries/mrzen/assets/js/TravelZen.js`):

- `price` - Basic price formatting with currency symbol
- `as_price` - Advanced formatting with locale support
- `thousands` - Number formatting with thousands separators

**PHP Helpers**:
- `ZenMoneyHelper` - Server-side currency utilities
- Currency symbols and formatting rules

### Currency Switcher

The `mod_zencurrencyswitcher` module provides:
- Dropdown list of active currencies
- Flag icons for visual identification
- Form submission to change user's currency
- Session persistence of selection

## Adding New Currencies

### Step 1: Add Currency Record

1. Navigate to **Administrator → Components → Zen Admin → Currencies**
2. Click **New** to create currency
3. Fill required fields:
   - **Code**: 3-letter ISO code (e.g., 'CAD')
   - **Symbol**: Currency symbol (e.g., 'C$')
   - **Title**: Full name (e.g., 'Canadian Dollar')
   - **State**: Set to **Published**

### Step 2: Add Exchange Rates

1. Go to **Administrator → Components → Zen Admin → Currency Rates**
2. Create rate from base currency to new currency:
   - **From**: Base currency (e.g., 'USD')
   - **To**: New currency (e.g., 'CAD')
   - **Rate**: Exchange rate (e.g., 1.35 for USD to CAD)
   - **Valid From**: Effective date
   - **Rounding**: Rounding increment (e.g., 1.00 for whole numbers)

### Step 3: Generate Prices

**Option A - Automatic (Recommended):**
- Edit any base currency price
- Plugin automatically generates all currency prices

**Option B - Manual Bulk Generation:**
- Use admin interface: **Tools → Generate All Forex Pricing**
- Processes all future-dated prices

### Step 4: Verify Display

1. Check currency appears in frontend switcher
2. Verify prices display correctly
3. Test currency switching functionality
4. Confirm price formatting matches expectations

## Configuration Options

### Component Parameters

In **Administrator → Components → Zen Admin → Options**:

- `base_currency` - Base currency for manual price entry
- `geoip_default_currency` - Fallback when GeoIP fails

### Domain-Specific Settings

Per-domain currency settings in `#__zendomains`:
- Default currency per website
- Currency grouping preferences
- Locale-specific formatting rules

## Technical Implementation Notes

### Price Storage

- All prices stored as DECIMAL(10,2) for precision
- Converted prices marked as auto-generated
- Manual editing disabled for non-base currencies

### Performance Considerations

- Exchange rates cached with date-based validity
- Frontend uses Angular filters for client-side formatting
- Database queries optimized with proper indexing

### Error Handling

- Missing exchange rates prevent price generation
- Invalid currencies fall back to base currency
- Rounding errors minimized through proper decimal handling

## Troubleshooting

### Common Issues

1. **Prices not converting**: Check exchange rates exist and are current
2. **Wrong currency showing**: Verify GeoIP configuration and session handling
3. **Formatting issues**: Check browser locale support and currency symbols
4. **Missing currencies**: Ensure currency is published and has valid rates

### Debugging Tools

- Enable plugin debugging in `forexpricing.php`
- Check browser console for Angular filter errors
- Monitor database for price generation logs
- Verify session currency storage

## Future Enhancements

### Potential Improvements

1. **API Integration**: Automatic exchange rate updates from financial APIs
2. **Historical Rates**: Track rate changes over time
3. **Dynamic Pricing**: Time-based or demand-based pricing adjustments
4. **Multi-Base Support**: Support multiple base currencies simultaneously
5. **Advanced Rounding**: More sophisticated rounding rules per market
